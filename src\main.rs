mod auth;
mod db;

use anyhow::{Context, Result};
use clap::Parser;
use std::process;

#[derive(Parse<PERSON>, Debug)]
#[command(author, version, about = "Checks if a user has sufficient core time to login via SSH", long_about = None)]
struct Args {
    /// Username to check
    #[arg(short, long)]
    username: String,
}

fn main() -> Result<()> {
    // Initialize environment
    env_logger::init();

    // Parse command line arguments
    let args = Args::parse();

    // Check if user is in whitelist
    if ["root", "dawn"].contains(&args.username.as_str()) {
        println!("User {} is in whitelist, allowing login", args.username);
        process::exit(0);
    }

    // Connect to database
    let db = db::Database::new()
        .context("Failed to connect to database")?;

    // Create auth checker
    let auth_checker = auth::AuthChecker::new(db);

    // Check if user can login
    match auth_checker.check_login(&args.username) {
        Ok(_) => {
            println!("User {} is allowed to login.", args.username);
            Ok(())
        }
        Err(e) => {
            println!("User {} is not allowed to login. {}", args.username, e);
            process::exit(1);
        }
    }
}
