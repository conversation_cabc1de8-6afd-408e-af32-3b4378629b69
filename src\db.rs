use anyhow::Result;
use mysql::{Pool, Opts, prelude::*};

pub struct Database {
    pool: Pool,
}

impl Database {
    /// Create a new database connection
    pub fn new() -> Result<Self> {
        // create user phadcloudsshchecker@'%' identified by 'Phad_sshchecker1269';
        // grant select(user_id, total_core_time, used_core_time, server_user ) on phadcloud_cluster.phadcloud_user_cluster to 'phadcloudsshchecker'@'%';
        // grant select(state, id) on phadcloud_cluster.phadcloud_users to 'phadcloudsshchecker'@'%';
        // flush privileges;
        let url = "mysql://phadcloudsshchecker:Phad_sshchecker1269@172.16.2.100:3306/phadcloud_cluster";
        let opts = Opts::from_url(url)?;
        let pool = Pool::new(opts)?;
        Ok(Self { pool })
    }

    /// Check if a user has enough core time to login and if the user is active
    pub fn check_user_core_time(&self, username: &str) -> Result<(bool, Option<i64>)> {
        let mut conn = self.pool.get_conn()?;

        let result: Option<i64> = conn.exec_first(
            "SELECT ( puc.total_core_time - puc.used_core_time ) AS result FROM  phadcloud_user_cluster puc INNER JOIN phadcloud_users pu ON puc.user_id = pu.id WHERE puc.server_user = ? AND pu.state = 1",
            (username,)
        )?;

        match result {
            Some(total_core_time) => {
                Ok((total_core_time >= 0, Some(total_core_time)))
            }
            None => Ok((false, None)), // User not found
        }
    }
}
