use anyhow::Result;
use mysql::{Pool, Opts, prelude::*};

pub struct Database {
    pool: Pool,
}

impl Database {
    /// Create a new database connection
    pub fn new() -> Result<Self> {
        // create user phadcloudsshchecker@'%' identified by 'Phad_sshchecker1269';
        // grant select(user_id, total_core_time, used_core_time, server_user ) on phadcloud_cluster.phadcloud_user_cluster to 'phadcloudsshchecker'@'%';
        // grant select(state, id) on phadcloud_cluster.phadcloud_users to 'phadcloudsshchecker'@'%';
        // flush privileges;
        let url = "mysql://phadcloudsshchecker:Phad_sshchecker1269@172.16.2.100:3306/phadcloud_cluster";
        let opts = Opts::from_url(url)?;
        let pool = Pool::new(opts)?;
        Ok(Self { pool })
    }

    /// Check if a user has enough core time to login and if the user is active
    /// Returns:
    /// - For active users (state = 1): (can_login, Some(remaining_core_time))
    /// - For disabled users (state != 1): (false, Some(-1))
    /// - For non-existent users: (false, Some(0))
    pub fn check_user_core_time(&self, username: &str) -> Result<(bool, Option<i64>)> {
        let mut conn = self.pool.get_conn()?;

        // First check if user exists and get their state and core time
        let result: Option<(i64, i32)> = conn.exec_first(
            "SELECT ( puc.total_core_time - puc.used_core_time ) AS remaining_time, pu.state FROM phadcloud_user_cluster puc INNER JOIN phadcloud_users pu ON puc.user_id = pu.id WHERE puc.server_user = ?",
            (username,)
        )?;

        match result {
            Some((remaining_core_time, state)) => {
                if state == 1 {
                    // User is active
                    Ok((remaining_core_time >= 0, Some(remaining_core_time)))
                } else {
                    // User exists but is disabled
                    Ok((false, Some(-1)))
                }
            }
            None => {
                // User not found in database
                Ok((false, Some(0)))
            }
        }
    }
}
