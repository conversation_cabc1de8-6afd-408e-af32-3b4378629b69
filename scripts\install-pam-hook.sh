#!/bin/bash
# <PERSON>ript to install the SSH login checker as a PAM hook

set -e

# Check if running as root
if [ "$EUID" -ne 0 ]; then
  echo "Please run as root"
  exit 1
fi

# Copy the binary to /usr/local/bin
echo "Installing binary to /usr/local/bin..."
cp phadcloud-ssh-checker /usr/local/bin/phadcloud-ssh-checker
chmod 755 /usr/local/bin/phadcloud-ssh-checker

# Create a PAM script for SSH
echo "Creating PAM script for SSH..."
cat > /usr/local/bin/ssh-login-check-pam << 'EOF'
#!/bin/bash
# Author: Dawn
# Date: 2025-04-08
# Desc: This script is executed by PAM to check if a user has enough core time

# PAM provides the username as the first argument
USERNAME="$PAM_USER"

# Fallback to the first argument if PAM_USER is not set
if [ -z "$USERNAME" ]; then
  USERNAME="$1"
fi

# If no username is provided, exit with failure
if [ -z "$USERNAME" ]; then
  exit 1
fi

# Set required environment variables
export HOSTNAME=$(hostname)
export RUST_LOG=info

# Check if the user has enough core time
OUTPUT=$(/usr/local/bin/phadcloud-ssh-checker --username "$USERNAME" 2>&1)
EXIT_CODE=$?

# Log the output and result
# echo "$(date '+%Y-%m-%d %H:%M:%S') Check result: $OUTPUT" >> /var/log/phadcloud-ssh-checker.log

if [ $EXIT_CODE -ne 0 ]; then
  # If the check fails, exit with failure
  exit 1
fi

# If the check passes, exit with success
exit 0
EOF

chmod 755 /usr/local/bin/ssh-login-check-pam

# Add PAM configuration
echo "Configuring PAM..."
PAM_CONFIG_FILE="/etc/pam.d/sshd"
PAM_LINE="auth       required     pam_exec.so /usr/local/bin/ssh-login-check-pam"

# Check if the line already exists
if grep -q "phadcloud-ssh-checker" "$PAM_CONFIG_FILE"; then
  echo "PAM configuration already exists"
else
  # Add the line to the PAM configuration
  echo "$PAM_LINE" >> "$PAM_CONFIG_FILE"
  echo "Added PAM configuration"
fi

echo "Installation complete!"
