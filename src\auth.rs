use anyhow::{anyhow, Result};
use log::info;

use crate::db::Database;

pub struct AuthChecker {
    db: Database,
}

impl AuthChecker {
    pub fn new(db: Database) -> Self {
        Self { db }
    }

    /// Check if a user is allowed to login based on their core time
    pub fn check_login(&self, username: &str) -> Result<()> {
        info!("Checking login permission for user: {}", username);

        let (allowed, core_time) = self.db.check_user_core_time(username)?;

        match (allowed, core_time) {
            (true, Some(time)) => {
                info!("User {} has sufficient core time: {}", username, time);
                Ok(())
            }
            (false, Some(time)) => {
                info!("User {} has insufficient core time: {}", username, time);
                Err(anyhow!("Access denied: You have insufficient core time remaining. Please contact the customer support."))
            }
            (_, None) => {
                info!("User {} not found in database, assuming system user - allowing login", username);
                Ok(())
            }
        }
    }
}
