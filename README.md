# SSH Login Checker

A Rust application that extends SSH login by checking if a user has sufficient core time in a MySQL database before allowing them to login.

## Features

- Checks user's `total_core_time` and `used_core_time` in the MySQL database
- Prevents login if the user's core time is negative
- Provides customizable error messages
- Integrates with SSH via PAM authentication
- Provides a wihtelist of users that can bypass the core time check. Example for `root` , `tjzs` user
- Check server hostname contains `login` and `phadcloud`, if not exit 0.

## Requirements

- Rust 1.56 or later
- MySQL database with a `phadcloud.phadcloud_users` table containing `server_user` and `total_core_time`, `used_core_time` columns
- Linux system with PAM (Pluggable Authentication Modules)

## Installation

### PAM Integration (Recommended)

Use the provided PAM installation script:

```bash
sudo ./scripts/install-pam-hook.sh
```

This will:
1. Install the binary to `/usr/local/bin/`
2. Set up a PAM hook that checks core time before allowing SSH logins

### Manual Installation

1. Build the project:
   ```bash
   # Need to remove `.cargo/config.toml` target.x86_64-unknown-linux-gnu configuration.
   cargo build --release

   # build for CentOS 7
   cargo build --release --target x86_64-unknown-linux-gnu
   ```
2. Copy the binary to your desired location:
   ```bash
   sudo cp target/release/phadcloud-ssh-checker /usr/local/bin/
   ```
3. Set up a PAM hook (see the PAM Integration section above)
   ```bash
   sudo ./scripts/install-pam-hook.sh
   ```

## Usage

### Command Line

```bash
# Check if a user can login
phadcloud-ssh-checker --username dawn10112
```

## How It Works

### PAM Integration

When a user attempts to login via SSH:

1. The SSH server calls the PAM authentication system
2. Our PAM hook is executed with the username of the person trying to login
3. The hook runs the `phadcloud-ssh-checker` to check if the user has sufficient core time
4. If the check passes, the SSH login proceeds to the next authentication step
5. If the check fails, an error message is displayed and the SSH login is denied

### SSH Client Wrapper

When a user attempts to use the SSH client:

1. Our wrapper script intercepts the command
2. The wrapper gets the username of the person running the command
3. It runs the `phadcloud-ssh-checker` to check if the user has sufficient core time
4. If the check passes, the original SSH command is executed
5. If the check fails, an error message is displayed and the SSH connection is blocked

## Notice

This wrapper named `license` for Material Studio Gateway subbmit job 
in `/home/<USER>/MaterialStudio/license` check user core time.

## License

PHADCLOUD
